package com.dep.biguo.widget;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.biguo.utils.dialog.MessageDialog;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.bean.ArticleBean;
import com.dep.biguo.bean.CourseBean;
import com.dep.biguo.bean.IconBean;
import com.dep.biguo.bean.ZkNewHomeBean;
import com.dep.biguo.databinding.HomeNewTabItemBinding;
import com.dep.biguo.databinding.HomeNewViewBinding;
import com.dep.biguo.mvp.presenter.HomePresenter;
import com.dep.biguo.mvp.ui.activity.AllVideoActivity;
import com.dep.biguo.mvp.ui.activity.ArticleActivity;
import com.dep.biguo.mvp.ui.activity.BiguoVipActivity;
import com.dep.biguo.mvp.ui.activity.BiguoVipOpenActivity;
import com.dep.biguo.mvp.ui.activity.ChapterActivity;
import com.dep.biguo.mvp.ui.activity.CounsellingZixuanActivity;
import com.dep.biguo.mvp.ui.activity.DayCardV3Activity;
import com.dep.biguo.mvp.ui.activity.ErrorCollActivity;
import com.dep.biguo.mvp.ui.activity.GraduateProxyActivity;
import com.dep.biguo.mvp.ui.activity.GroupActivity;
import com.dep.biguo.mvp.ui.activity.GroupGoodsActivity;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.activity.InternetStudyGoodsActivity;
import com.dep.biguo.mvp.ui.activity.InviteMainActivity;
import com.dep.biguo.mvp.ui.activity.MainActivity;
import com.dep.biguo.mvp.ui.activity.NewUserActivity;
import com.dep.biguo.mvp.ui.activity.OrderSecretActivity;
import com.dep.biguo.mvp.ui.activity.SchoolRecommendActivity;
import com.dep.biguo.mvp.ui.activity.SearchUrlActivity;
import com.dep.biguo.mvp.ui.activity.SecretListActivity;
import com.dep.biguo.mvp.ui.activity.SelectCourseActivity;
import com.dep.biguo.mvp.ui.activity.ShopDetailActivity;
import com.dep.biguo.mvp.ui.activity.TestPaperListActivity;
import com.dep.biguo.mvp.ui.activity.TextBooksActivity;
import com.dep.biguo.mvp.ui.activity.TopicListActivity;
import com.dep.biguo.mvp.ui.activity.TruePaperNewActivity;
import com.dep.biguo.mvp.ui.activity.VideoTypeListActivity;
import com.dep.biguo.mvp.ui.adapter.HomeIconAdapter;
import com.dep.biguo.mvp.ui.adapter.HomeNewAdapter;
import com.dep.biguo.mvp.ui.adapter.HomeRecommendAdapter;
import com.dep.biguo.utils.BannerRoundImageLoader;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.TimeFormatUtils;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.widget.pagemenu.PageMenuViewHolderCreator;
import com.dep.biguo.wxapi.WxMinApplication;
import com.google.android.material.tabs.TabLayout;
import com.hjq.toast.ToastUtils;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;
import com.youth.banner.Banner;
import com.youth.banner.BannerConfig;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class HomeNewHeadView extends ConstraintLayout {
    public HomeNewViewBinding homeViewBinding;
    private HomePresenter mPresenter;

    public HomeIconAdapter mIconAdapter;//icon图标
    public HomeRecommendAdapter recommendAdapter;//推荐列表
    public HomeNewAdapter mNewsAdapter;//新闻

    private ZkNewHomeBean zkHomeBean;
    private CourseBean.CourseItemBean courseBean;

    public HomeNewHeadView(@NonNull Context context, HomePresenter presenter) {
        super(context);
        this.mPresenter = presenter;
        init();
    }

    public HomeNewHeadView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public HomeNewHeadView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public void setmPresenter(HomePresenter mPresenter) {
        this.mPresenter = mPresenter;
    }

    public void init(){
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        setLayoutParams(layoutParams);

        homeViewBinding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.home_new_view, this, true);
        homeViewBinding.setOnClickListener(this);

        mIconAdapter = new HomeIconAdapter(new ArrayList<>());
        recommendAdapter = new HomeRecommendAdapter();
        mNewsAdapter = new HomeNewAdapter(new ArrayList<>());

        //初始化顶部banner图
        initBanner(homeViewBinding.headBannerView);
        //金刚区适配器
        initIconRecyclerView();
        //中间的banner图
        initBanner(homeViewBinding.middleBannerView);
        //绑定视频课程适配器
        initRecommendAdapter();
        //绑定新闻适配器
        initNewsAdapter();
    }

    public void createIcon(){
        homeViewBinding.courseAllLayout.setVisibility(isEnrollCourse(false) ? VISIBLE : GONE);
        //当tab的数量超过课程数量，就把多余的tab移除
        while (homeViewBinding.tabLayout.getTabCount() > zkHomeBean.getCourses().size()){
            homeViewBinding.tabLayout.removeTabAt(homeViewBinding.tabLayout.getTabCount() - 1);
        }

        //创建课程选择栏
        for (int i=0; i<zkHomeBean.getCourses().size(); i++){
            TabLayout.Tab tab = homeViewBinding.tabLayout.getTabAt(i);
            if(tab == null){
                View tabView = LayoutInflater.from(getContext()).inflate(R.layout.home_new_tab_item, null);
                tab = homeViewBinding.tabLayout.newTab().setCustomView(tabView);
                homeViewBinding.tabLayout.addTab(tab);
            }
            HomeNewTabItemBinding tabBinding = DataBindingUtil.bind(tab.getCustomView());
            tabBinding.getRoot().setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            LinearLayout linearLayout = (LinearLayout) tabBinding.getRoot().getParent();
            linearLayout.setPadding(DisplayHelper.dp2px(getContext(), i == 0 ? 0 : 5),0,DisplayHelper.dp2px(getContext(), 5),0);
            linearLayout.setGravity(Gravity.CENTER);
            final int index = i;
            tabBinding.getRoot().setOnClickListener(v -> {
                String selectCode = zkHomeBean.getCourses().get(index).getCode();
                if(!TextUtils.equals(selectCode, zkHomeBean.getCode())){
                    mPresenter.getHomeData(selectCode, true);
                }
            });

            CourseBean.CourseItemBean courseBean = zkHomeBean.getCourses().get(i);
            tabBinding.courseNameView.setText(String.format("%s%s", courseBean.getCode(), courseBean.getName()));
            String formatData = TimeFormatUtils.inMorningOrAfternoon(courseBean.getExam_time());
            tabBinding.courseTimeView.setText(AppUtil.isEmpty(formatData, "暂未公布考试计划"));
        }

        //手动调用选中
        homeViewBinding.tabLayout.post(() -> {
            int selectIndex = 0;
            //找到本次数据对应课程进行选中
            for (int i=0; i<zkHomeBean.getCourses().size(); i++){
                if(TextUtils.equals(zkHomeBean.getCode(), zkHomeBean.getCourses().get(i).getCode())){
                    selectIndex = i;
                    break;
                }
            }
            //当返回的课程不含课程代码时，选中的课程要值为空
            if(TextUtils.isEmpty(zkHomeBean.getCode())) {
                courseBean = null;
            }
            if(isEnrollCourse(false)) {
                courseBean = zkHomeBean.getCourses().get(selectIndex);
                if (homeViewBinding.tabLayout.getTabCount() > selectIndex) {
                    homeViewBinding.tabLayout.getTabAt(selectIndex).select();
                }
            }
        });
        setIconList(zkHomeBean.getIcon1());

    }

    public void setIconList(List<IconBean> list){
        //分页菜单
        int rowSpace = DisplayHelper.dp2px(getContext(), 10);
        homeViewBinding.pageMenuView.setHolderCreator(list, 5, 2, rowSpace, new PageMenuViewHolderCreator<IconBean>() {
            private IconView homeIconView;
            @Override
            public int getLayoutId() {
                return R.layout.home_icon_item;
            }

            @Override
            public void initView(View view) {
                homeIconView = view.findViewById(R.id.homeIconView);
                homeIconView.setSpace(DisplayHelper.dp2px(getContext(), 5));
            }

            @Override
            public void bindView(RecyclerView.ViewHolder holder, IconBean data, int pos) {
                homeIconView.setData(data.getImg(), data.getName());
                holder.itemView.setOnClickListener(v -> {
                    //埋点每个icon的点击次数
                    new UmengEventUtils(getContext())
                            .addParams("onPage", homeViewBinding.pageMenuView.getCurrentPage() + 1)
                            .addParams("icon_type", data.getName())
                            .pushEvent(UmengEventUtils.CLICK_ICON);

                    boolean isNeedLogin = data.getNeed_login() == StartFinal.YES;
                    boolean isBuy = data.getIs_buy() == StartFinal.YES;
                    boolean isEnable = data.getIs_enable() == StartFinal.YES;
                    iconStartActivity(data.getType(), data.getXcx_path(), data.getTarget_url(), isNeedLogin, isBuy, isEnable);
                });
            }
        });

        homeViewBinding.pageMenuView.setOnPageListener(position -> {
            int checkId = homeViewBinding.indicatorGroup.getChildAt(position).getId();
            homeViewBinding.indicatorGroup.check(checkId);
        });

        //翻页指示器
        homeViewBinding.indicatorGroup.removeAllViews();
        for (int i=0; i<homeViewBinding.pageMenuView.getPageCount(); i++){
            RadioButton radioButton = new RadioButton(getContext());
            radioButton.setButtonDrawable(null);
            radioButton.setBackground(AppUtil.getDrawableRes(getContext(), R.drawable.check_indicator));
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(DisplayHelper.dp2px(getContext(), 12), DisplayHelper.dp2px(getContext(), 4 ));
            layoutParams.setMargins(i == 0 ? 0 : DisplayHelper.dp2px(getContext(), 4), 0, 0, 0);
            radioButton.setLayoutParams(layoutParams);
            homeViewBinding.indicatorGroup.addView(radioButton);
        }
        int checkId = homeViewBinding.indicatorGroup.getChildAt(0).getId();
        homeViewBinding.indicatorGroup.check(checkId);
    }

    private void initIconRecyclerView(){
        homeViewBinding.iconRecyclerView.addItemDecoration(new AlignGridItemDecoration(AlignGridItemDecoration.ALIGN_SIDES));
        mIconAdapter.setItemWidth(DisplayHelper.getWindowWidth(getContext()) / 5);
        mIconAdapter.bindToRecyclerView(homeViewBinding.iconRecyclerView);
        mIconAdapter.setOnItemClickListener((adapter, view, position) -> {
            IconBean icon = mIconAdapter.getItem(position);
            //埋点每个icon的点击次数
            new UmengEventUtils(getContext())
                    .addParams("onPage", 1)
                    .addParams("icon_type", icon.getName())
                    .pushEvent(UmengEventUtils.CLICK_ICON);

            boolean isNeedLogin = icon.getNeed_login() == StartFinal.YES;
            boolean isBuy = icon.getIs_buy() == StartFinal.YES;
            boolean isEnable = icon.getIs_enable() == StartFinal.YES;
            if ("smart_assemble".equals(icon.getType())) {
                com.dep.biguo.mvp.ui.activity.SmartAssembleActivity.start(getContext());
                return;
            }
            iconStartActivity(icon.getType(), icon.getXcx_path(), icon.getTarget_url(), isNeedLogin, isBuy, isEnable);
        });
    }

    private void initRecommendAdapter(){
        //添加流布局管理器
        MaxCountItemManager layoutManager = new MaxCountItemManager(getContext());
        layoutManager.setSpaceHorizontal(DisplayHelper.dp2px(getContext(), 10));
        layoutManager.setSpaceVertical(DisplayHelper.dp2px(getContext(), 10));
        homeViewBinding.recommendRecyclerView.setLayoutManager(layoutManager);
        homeViewBinding.recommendRecyclerView.setAdapter(recommendAdapter);
        recommendAdapter.setOnItemClickListener((adapter, view, position) -> {
            ZkNewHomeBean.Recommend recommend = recommendAdapter.getItem(position);
            if(StartFinal.BOOK.equals(recommend.getType())){
                Intent intent = new Intent(getContext(), ShopDetailActivity.class);
                intent.putExtra(ShopDetailActivity.GOODSID, recommend.getProduct_id());
                getContext().startActivity(intent);
            }else {
                GroupGoodsActivity.start(getActivity(), recommend.getCode(), recommend.getType(), recommend.getSource_type(), recommend.getProduct_id(), 0);
            }
        });
    }

    private void initNewsAdapter(){
        homeViewBinding.newsRecyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal));
        mNewsAdapter.bindToRecyclerView(homeViewBinding.newsRecyclerView);
        mNewsAdapter.setOnItemClickListener((adapter, view, position) -> {
            ArticleBean news = mNewsAdapter.getItem(position);
            if(!TextUtils.isEmpty(news.getXcx_path()) && AppUtil.isInstallWechat(getContext())) {
                WxMinApplication.StartWechat(getContext(), news.getXcx_path(), news.getTarget_url());
                mPresenter.redNews(news);

            }else {
                HtmlActivity.start(getContext(), news.getTarget_url(), HtmlActivity.ARTICLE_TYPE, news.getId());
            }
        });

    }

    /**初始化banner图
     * <p color=#fff>注：Banner.setImages()的数组与Banner.update()的数组不能是同一个，否则不会触发刷新</p>
     * @param banner
     */
    private void initBanner(Banner banner) {
        //去掉左右边距，根据比例计算banner图的高度
        banner.setImages(new ArrayList<>());
        banner.setImageLoader(new BannerRoundImageLoader());
        banner.setBannerStyle(BannerConfig.NOT_INDICATOR);
        banner.start();
        banner.setOnBannerListener(position -> {
            ZkNewHomeBean.Banner bannerBean = mPresenter.getBannerBean(banner == homeViewBinding.headBannerView ? 1 : 2, position);
            if(bannerBean == null) return;

            //埋点统计轮播图的点击次数
            new UmengEventUtils(getContext())
                    .addParams("banner_id", bannerBean.getId()+"")
                    .addParams("banner_title", TextUtils.isEmpty(bannerBean.getName()) ? "" : bannerBean.getName())
                    .pushEvent(UmengEventUtils.CLICK_BANNER);

            if(bannerBean.getNeed_login() == 1 && !MainAppUtils.checkLogin(getContext())) return;

            if(bannerBean.getType() == 123){//学梦计划
                HtmlActivity.start(getContext(), bannerBean.getTarget_url());

            }else if(bannerBean.getType() == 14 || bannerBean.getType() == 15) {//14未购买笔果折扣卡，15已购买笔果折扣卡
                if(!MainAppUtils.checkLogin(getContext())) return;
                if(UserCache.isMemberShip()){
                    ArmsUtils.startActivity(BiguoVipActivity.class);
                }else {
                    ArmsUtils.startActivity(BiguoVipOpenActivity.class);
                }
            }else if(bannerBean.getType() == 16) {//名校推荐
                ArmsUtils.startActivity(SchoolRecommendActivity.class);

            }else if(bannerBean.getType() == 18) {//网络助学
                InternetStudyGoodsActivity.start(getContext());

            }else if(bannerBean.getType() == 23) {//精讲视频
                AllVideoActivity.start(getContext(), StartFinal.VIDEO1);

            }else if(bannerBean.getType() == 24) {//串讲视频
                AllVideoActivity.start(getContext(), StartFinal.VIDEO2);

            }else if(bannerBean.getType() == 25) {//特训班视频
                AllVideoActivity.start(getContext(), StartFinal.VIDEO3);

            }else if(bannerBean.getType() == 27) {//分销首页
                ArmsUtils.startActivity(InviteMainActivity.class);

            }else if(bannerBean.getType() == 37) {//毕业代办申请·
                GraduateProxyActivity.start(getContext());

            }else if(!TextUtils.isEmpty(bannerBean.getXcx_path())) {//跳转小程序
                WxMinApplication.StartEncoderUrlToMinAppOrApp(getContext(), bannerBean.getXcx_path(), bannerBean.getTarget_url(), bannerBean.getOpen_wx() == 1);

            }else if(!TextUtils.isEmpty(bannerBean.getTarget_url())){//跳转H5页面
                HtmlActivity.start(getActivity(), bannerBean.getTarget_url());
            }

        });
    }

    public void onClick(View view) {
        if(view == homeViewBinding.moreView){
            SelectCourseActivity.Start(getContext());

        }else if(view == homeViewBinding.newUserActivityView){//新人活动
            if(!MainAppUtils.checkLogin(getContext())) return;
            NewUserActivity.start(getContext());

        }else if(view == homeViewBinding.vipGroupView){//VIP题库
            if(!isEnrollCourse(true)) return;//检查是否已选择课程
            if(!isSelectCourse()) return;

            //埋点考试神器VIP题库的点击次数
            new UmengEventUtils(getContext())
                    .addParams("click_vip_or_yami", "vip")
                    .pushEvent(UmengEventUtils.CLICK_VIP_OR_YAMI);

            for(IconBean iconBean : zkHomeBean.getIcon3()) {
                if (TextUtils.equals(iconBean.getType(), StartFinal.VIP)) {
                    if(iconBean.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(getContext())) return;

                    if(iconBean.getIs_enable() == StartFinal.NO || iconBean.getIs_buy() == StartFinal.YES){
                        TopicListActivity.Start(getContext(), courseBean.getCourses_id(), courseBean.getName(), courseBean.getCode(), PracticeHelper.PRACTICE_VIP);
                    }else if(iconBean.getIs_enable() == StartFinal.YES){
                        GroupGoodsActivity.start(getActivity(), courseBean.getCode(), iconBean.getType());
                    }
                    break;
                }
            }

        }else if(view == homeViewBinding.secretGroupView){//考前押密
            if(!isEnrollCourse(true)) return;
            if(!isSelectCourse()) return;//检查是否已选择课程
            //埋点考试神器押密题库的点击次数
            new UmengEventUtils(getContext())
                    .addParams("click_vip_or_yami", "yami")
                    .pushEvent(UmengEventUtils.CLICK_VIP_OR_YAMI);

            for(IconBean iconBean : zkHomeBean.getIcon3()) {
                if (TextUtils.equals(iconBean.getType(), StartFinal.YAMI)) {
                    if(iconBean.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(getContext())) return;

                    if(iconBean.getIs_enable() == StartFinal.NO){
                        //不可预订，Toast提示
                        ToastUtils.show("当前科目押密未上架，敬请谅解！");
                    } else if(iconBean.getIs_buy() == StartFinal.NO) {//未购买，跳转到商品详情页
                        GroupGoodsActivity.start(getActivity(), courseBean.getCode(), iconBean.getType());

                    }else if(iconBean.getIs_buy() == StartFinal.YES){//已购买则跳转到学习页
                        SecretListActivity.start(getContext(), courseBean.getCode(), courseBean.getName());
                    }
                    break;
                }else if(TextUtils.equals(iconBean.getType(), StartFinal.YAMI_RESERVE)){
                    if(iconBean.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(getContext())) return;

                    if(iconBean.getIs_enable() == -1 && iconBean.getIs_enable() == 0){
                        //不可预订，Toast提示
                        ToastUtils.show("当前科目预定押密未上架，敬请谅解！");
                    }else {
                        OrderSecretActivity.start(getContext(), courseBean.getName(), courseBean.getCode());
                    }
                }
            }

        }else if(view == homeViewBinding.examNewsView){//考试资讯
            moveIndicator(homeViewBinding.examNewsView, homeViewBinding.schoolNewsView, homeViewBinding.newsIndicatorView);
            homeViewBinding.newsIndicatorView.setTag(ArticleActivity.HISTORY_NEWS);
            mPresenter.getNewList(HomePresenter.EXAM_NEW);

        }else if(view == homeViewBinding.schoolNewsView){//学校新闻
            moveIndicator(homeViewBinding.schoolNewsView, homeViewBinding.examNewsView, homeViewBinding.newsIndicatorView);
            homeViewBinding.newsIndicatorView.setTag(ArticleActivity.SCHOOL_NEWS);
            mPresenter.getNewList(HomePresenter.SCHOOL_NEW);

        }else if(view == homeViewBinding.moreNewsView){//更多新闻
            ArticleActivity.start(getActivity(), homeViewBinding.newsIndicatorView.getTag() == null ? ArticleActivity.HISTORY_NEWS : (Integer) homeViewBinding.newsIndicatorView.getTag());

        }else if(view == homeViewBinding.moreCircleView){//更多动态
            Activity activity = getActivity();
            if(activity instanceof MainActivity){
                MainActivity mainActivity = (MainActivity) activity;
                mainActivity.scrollViewPager(3);
            }
        }
    }

    private boolean isSelectCourse(){
        if(courseBean == null){
            if(AppUtil.isEmpty(zkHomeBean.getCourses())){
                ToastUtils.show("无可选择的课程");
            }else {
                ToastUtils.show("请选择课程");
            }
            return false;
        }
        return true;
    }

    private boolean isEnrollCourse(boolean isOpenEnrollDialog){
        if(!UserCache.getHomeShowEnrollCourse()){
            if(isOpenEnrollDialog) {
                mPresenter.getCourse();
            }
            return false;
        }
        return true;
    }

    public void moveIndicator(TextView targetView, TextView otherView, View indicatorView){
        //指示器更换对齐目标
        LayoutParams indicatorLP = (LayoutParams)indicatorView.getLayoutParams();
        indicatorLP.startToStart = targetView.getId();
        indicatorLP.topToBottom = targetView.getId();
        indicatorLP.endToEnd = targetView.getId();
        indicatorView.setLayoutParams(indicatorLP);
        //改变字体大小和颜色
        targetView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18);
        targetView.setTextColor(getResources().getColor(R.color.tblack));
        otherView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
        otherView.setTextColor(getResources().getColor(R.color.tblack3));
    }

    private void iconStartActivity(String type, String path, String targetUrl, boolean needLogin, boolean isBuy, boolean isEnable){
        //设置需要检测的type值集合
        if(needLogin && !MainAppUtils.checkLogin(getContext())) return;
        List<String> checkSelectCourseTypeList = Arrays.asList("vip","yami","yami_reserve","video", "prac", "chapter", "real_paper", "high_frequency", "simu");
        if(checkSelectCourseTypeList.contains(type) && !isEnrollCourse(true)) return;//检查是否已选择课程


        if(type.contains("video")) {//视频课程
            if(!isSelectCourse()) return;
            VideoTypeListActivity.start(getContext(), courseBean.getCourses_id(), courseBean.getName(), courseBean.getCode());

        }else if(type.equals("prac")) {//免费题库
            if(!isSelectCourse()) return;
            TopicListActivity.Start(getActivity(), courseBean.getCourses_id(), courseBean.getName(), courseBean.getCode(), PracticeHelper.PRACTICE_COURSE);

        }else if(type.equals("real_paper")) {//历年真题
            TruePaperNewActivity.start(getContext(), courseBean.getName(), courseBean.getCode(), courseBean.getCourses_id());

        }else if(type.equals("chapter")) {//章节训练
            if(!isSelectCourse()) return;
            ChapterActivity.Start(getContext(), courseBean.getCourses_id(), courseBean.getName(), courseBean.getCode());

        }else if(type.equals("simu")) {//模拟试卷
            if(!isSelectCourse()) return;
            TestPaperListActivity.start(getContext(), courseBean.getCode(), courseBean.getName());

        }else if(type.equals("high_frequency")) {//高频考点
            if(!isSelectCourse()) return;
            if(isBuy || !isEnable) {
                TopicListActivity.Start(getActivity(), courseBean.getCourses_id(), courseBean.getName(), courseBean.getCode(), PracticeHelper.PRACTICE_TYPE_HIGH);
            }else {
                GroupGoodsActivity.start(getActivity(), courseBean.getCode(), StartFinal.HIGH_FREQUENCY);
            }

        }else if(type.equals("error_collection")) {//错题·收藏
            ErrorCollActivity.Start(getContext());

        }else if(type.equals("zxjf")) {//助学加分
            InternetStudyGoodsActivity.start(getContext());

        }else if(type.equals("book")) {//自考教材
            TextBooksActivity.start(getContext(), 2);

        }else if(type.equals("fdb")) {//辅导班
            CounsellingZixuanActivity.start(getContext());

        }else if(type.equals("zhcx")) {//综合查询
            ArmsUtils.startActivity(SearchUrlActivity.class);

        }else if(type.equals("pintuan")) {//我要拼团
            GroupActivity.Start(getContext(), "", 0);

        }else if(type.equals("zixun")) {//咨询老师
            WxMinApplication.StartWechat(getContext());

        }else if(type.equals("sign")) {//每日打卡
            ArmsUtils.startActivity(DayCardV3Activity.class);

        }else if(type.equals("vip")) {//VIP题库
            if(isBuy || !isEnable){
                TopicListActivity.Start(getContext(), courseBean.getCourses_id(), courseBean.getName(), courseBean.getCode(), PracticeHelper.PRACTICE_VIP);
            }else {
                GroupGoodsActivity.start(getActivity(), courseBean.getCode(), type);
            }

        }else if(type.equals(StartFinal.YAMI_RESERVE)) {//预定押密
            OrderSecretActivity.start(getContext(), courseBean.getName(), courseBean.getCode());

        }else if(type.equals("yami")) {//押密
            if(!isBuy) {//未购买，跳转到商品详情页
                GroupGoodsActivity.start(getActivity(), courseBean.getCode(), type);

            }else {//已购买则跳转到学习页
                SecretListActivity.start(getContext(), courseBean.getCode(), courseBean.getName());
            }

        }else if(type.equals("member")) {//笔果折扣卡
            if(UserCache.isMemberShip()){
                ArmsUtils.startActivity(BiguoVipActivity.class);
            }else {
                ArmsUtils.startActivity(BiguoVipOpenActivity.class);
            }

        }else if(type.equals("douyin")) {//抖音直播
            Activity activity = AppManager.getAppManager().getTopActivity();
            if(activity instanceof MainActivity) {
                new MessageDialog.Builder(((MainActivity)activity).getSupportFragmentManager())
                        .setTitle("温馨提醒")
                        .setContent("即将跳转至抖音，是否继续？")
                        .setNegativeText("取消")
                        .setPositiveText("继续")
                        .setPositiveClickListener((OnClickListener) v -> {
                            Uri uri = Uri.parse(targetUrl);
                            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                            intent.setPackage(getContext().getPackageName());
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            getContext().startActivity(intent);
                        }).builder()
                        .show();
            }

        }else if(type.equals("elite_school")) {//名校推荐
            ArmsUtils.startActivity(SchoolRecommendActivity.class);

        }else if(type.equals("network_aid")) {//网络助学
            InternetStudyGoodsActivity.start(getContext());

        }else if(!TextUtils.isEmpty(path)) {//跳转小程序
            if(needLogin && !MainAppUtils.checkLogin(getContext())) return;
            if(TextUtils.equals(type, "kf") || TextUtils.equals(type, "crgk") || TextUtils.equals(type, "xzk") || TextUtils.equals(type, "zbtd")){
                WxMinApplication.StartWechatAddService(getContext(), path, targetUrl);
            }else {
                WxMinApplication.StartWechat(getContext(), path, targetUrl);
            }

        }else if(!TextUtils.isEmpty(targetUrl)){//跳转H5页面, 我要报名、积分入户、学梦计划
            HtmlActivity.start(getActivity(), targetUrl);

        }
    }

    public void setHomeData(ZkNewHomeBean zkHomeBean) {
        this.zkHomeBean = zkHomeBean;

        //初始化顶部banner图
        homeViewBinding.headBannerView.update(mPresenter.getBannerImgList(zkHomeBean, 1));
        //初始化中间banner图
        homeViewBinding.middleBannerView.update(mPresenter.getBannerImgList(zkHomeBean, 2));
        homeViewBinding.middleBannerView.setVisibility(AppUtil.isEmpty(mPresenter.getBannerImgList(zkHomeBean, 2)) ? GONE : VISIBLE);
        //新人活动
        homeViewBinding.newUserActivityView.setVisibility(TextUtils.isEmpty(zkHomeBean.getIs_newcomers()) ? View.GONE : View.VISIBLE);
        ImageLoader.loadImageNoPlaceholder(homeViewBinding.newUserActivityView, zkHomeBean.getIs_newcomers());

        //获取金刚区的icon
        if(!AppUtil.isEmpty(zkHomeBean.getIcon1())){
            createIcon();
        }

        if(!AppUtil.isEmpty(zkHomeBean.getIcon2())){
            // 将“智能组卷”入口插入到底部一排按钮的首位
            IconBean smart = new IconBean();
            smart.setName("智能组卷");
            smart.setType("smart_assemble");
            smart.setIs_enable(1);
            smart.setIs_buy(1);
            zkHomeBean.getIcon2().add(0, smart);
            //开始刷题按钮底部的一排按钮
            homeViewBinding.line0View.setVisibility(!zkHomeBean.getIcon2().isEmpty() ? View.VISIBLE : View.GONE);
            homeViewBinding.iconRecyclerView.setVisibility(!zkHomeBean.getIcon2().isEmpty() ? View.VISIBLE : View.GONE);
            mIconAdapter.setNewData(zkHomeBean.getIcon2());
        }

        //获取考试神器的icon
        List<IconBean> groupList = zkHomeBean.getIcon3();
        String secretGroupImg = null;
        String vipGroupImg = null;
        //加载考试神器的图片
        for(IconBean icon : groupList){
            if(icon.getType().equals(StartFinal.YAMI)){
                secretGroupImg = icon.getImg();
            }else if(icon.getType().equals(StartFinal.YAMI_RESERVE)){
                secretGroupImg = icon.getImg();
            }else if(icon.getType().equals(StartFinal.VIP)){
                vipGroupImg = icon.getImg();
            }
        }
        ImageLoader.loadImageNoPlaceholder(homeViewBinding.secretGroupView, secretGroupImg);
        ImageLoader.loadImageNoPlaceholder(homeViewBinding.vipGroupView, vipGroupImg);

        //考试必备
        recommendAdapter.setNewData(zkHomeBean.getRecommend_products());
        //考试资讯/学校新闻
        mNewsAdapter.setNewData(zkHomeBean.getFindings());
    }

    public void setNews(List<ArticleBean> list){
        mNewsAdapter.setNewData(list);
    }

    public void notifyNews(){
        mNewsAdapter.notifyDataSetChanged();
    }

    public void shoExamNews(){
        onClick(homeViewBinding.examNewsView);
    }

    public void setShowCircleTitleView(boolean isShow){
        int visibility = isShow ? View.VISIBLE : View.GONE;
        homeViewBinding.line6View.setVisibility(visibility);
        homeViewBinding.circleTitleView.setVisibility(visibility);
        homeViewBinding.moreCircleView.setVisibility(visibility);
    }

    public Activity getActivity(){
        return AppManager.getAppManager().getTopActivity();
    }
}
